import 'package:cloud_firestore/cloud_firestore.dart';

class Coach {
  final String id;
  final String name;
  final String specialty;
  final String description;
  final String? photoUrl;
  final Location location;
  final String contact;
  final String experience;
  final String priceRange;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status; // active/inactive
  final String userId; // 关联的用户ID

  Coach({
    required this.id,
    required this.name,
    required this.specialty,
    required this.description,
    this.photoUrl,
    required this.location,
    required this.contact,
    required this.experience,
    required this.priceRange,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.userId,
  });

  factory Coach.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Coach(
      id: doc.id,
      name: data['name'] ?? '',
      specialty: data['specialty'] ?? '',
      description: data['description'] ?? '',
      photoUrl: data['photoUrl'],
      location: Location.fromMap(data['location'] ?? {}),
      contact: data['contact'] ?? '',
      experience: data['experience'] ?? '',
      priceRange: data['priceRange'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: data['status'] ?? 'active',
      userId: data['userId'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'specialty': specialty,
      'description': description,
      'photoUrl': photoUrl,
      'location': location.toMap(),
      'contact': contact,
      'experience': experience,
      'priceRange': priceRange,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'status': status,
      'userId': userId,
    };
  }

  Coach copyWith({
    String? id,
    String? name,
    String? specialty,
    String? description,
    String? photoUrl,
    Location? location,
    String? contact,
    String? experience,
    String? priceRange,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? status,
    String? userId,
  }) {
    return Coach(
      id: id ?? this.id,
      name: name ?? this.name,
      specialty: specialty ?? this.specialty,
      description: description ?? this.description,
      photoUrl: photoUrl ?? this.photoUrl,
      location: location ?? this.location,
      contact: contact ?? this.contact,
      experience: experience ?? this.experience,
      priceRange: priceRange ?? this.priceRange,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      userId: userId ?? this.userId,
    );
  }
}

class Location {
  final String province;
  final String city;
  final String district;

  Location({
    required this.province,
    required this.city,
    required this.district,
  });

  factory Location.fromMap(Map<String, dynamic> map) {
    return Location(
      province: map['province'] ?? '',
      city: map['city'] ?? '',
      district: map['district'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'province': province,
      'city': city,
      'district': district,
    };
  }

  String get fullAddress => '$province $city $district';

  Location copyWith({
    String? province,
    String? city,
    String? district,
  }) {
    return Location(
      province: province ?? this.province,
      city: city ?? this.city,
      district: district ?? this.district,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Location &&
        other.province == province &&
        other.city == city &&
        other.district == district;
  }

  @override
  int get hashCode => province.hashCode ^ city.hashCode ^ district.hashCode;

  @override
  String toString() => fullAddress;
}
