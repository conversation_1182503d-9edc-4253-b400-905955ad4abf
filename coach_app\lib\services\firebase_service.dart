import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import '../models/coach.dart';
import '../models/user.dart';
import '../utils/constants.dart';

class FirebaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  // 教练相关操作
  static Future<List<Coach>> getCoaches({
    int limit = AppConstants.pageSize,
    DocumentSnapshot? lastDocument,
    String? specialty,
    String? city,
    String? keyword,
  }) async {
    try {
      Query query = _firestore
          .collection('coaches')
          .where('status', isEqualTo: 'active')
          .orderBy('createdAt', descending: true);

      // 添加筛选条件
      if (specialty != null && specialty.isNotEmpty) {
        query = query.where('specialty', isEqualTo: specialty);
      }

      if (city != null && city.isNotEmpty) {
        query = query.where('location.city', isEqualTo: city);
      }

      // 分页
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot snapshot = await query.get();
      List<Coach> coaches = snapshot.docs.map((doc) => Coach.fromFirestore(doc)).toList();

      // 如果有关键词搜索，在客户端进行过滤
      if (keyword != null && keyword.isNotEmpty) {
        coaches = coaches.where((coach) {
          return coach.name.toLowerCase().contains(keyword.toLowerCase()) ||
              coach.description.toLowerCase().contains(keyword.toLowerCase()) ||
              coach.specialty.toLowerCase().contains(keyword.toLowerCase());
        }).toList();
      }

      return coaches;
    } catch (e) {
      throw Exception('获取教练列表失败: $e');
    }
  }

  static Future<Coach?> getCoachById(String coachId) async {
    try {
      final DocumentSnapshot doc = await _firestore.collection('coaches').doc(coachId).get();
      if (doc.exists) {
        return Coach.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('获取教练详情失败: $e');
    }
  }

  static Future<void> createCoach(Coach coach) async {
    try {
      await _firestore.collection('coaches').add(coach.toFirestore());
    } catch (e) {
      throw Exception('创建教练信息失败: $e');
    }
  }

  static Future<void> updateCoach(Coach coach) async {
    try {
      await _firestore.collection('coaches').doc(coach.id).update(coach.toFirestore());
    } catch (e) {
      throw Exception('更新教练信息失败: $e');
    }
  }

  static Future<void> deleteCoach(String coachId) async {
    try {
      await _firestore.collection('coaches').doc(coachId).update({'status': 'inactive'});
    } catch (e) {
      throw Exception('删除教练信息失败: $e');
    }
  }

  static Future<List<Coach>> getCoachesByUserId(String userId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('coaches')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => Coach.fromFirestore(doc)).toList();
    } catch (e) {
      throw Exception('获取用户教练信息失败: $e');
    }
  }

  // 用户相关操作
  static Future<AppUser?> getUserById(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return AppUser.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('获取用户信息失败: $e');
    }
  }

  static Future<void> createUser(AppUser user) async {
    try {
      await _firestore.collection('users').doc(user.id).set(user.toFirestore());
    } catch (e) {
      throw Exception('创建用户信息失败: $e');
    }
  }

  static Future<void> updateUser(AppUser user) async {
    try {
      await _firestore.collection('users').doc(user.id).update(user.toFirestore());
    } catch (e) {
      throw Exception('更新用户信息失败: $e');
    }
  }

  // 文件上传
  static Future<String> uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path);
      final uploadTask = await ref.putFile(file);
      return await uploadTask.ref.getDownloadURL();
    } catch (e) {
      throw Exception('文件上传失败: $e');
    }
  }

  static Future<void> deleteFile(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
    } catch (e) {
      throw Exception('文件删除失败: $e');
    }
  }

  // 获取专长统计
  static Future<Map<String, int>> getSpecialtyStats() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('coaches')
          .where('status', isEqualTo: 'active')
          .get();

      Map<String, int> stats = {};
      for (var doc in snapshot.docs) {
        final coach = Coach.fromFirestore(doc);
        stats[coach.specialty] = (stats[coach.specialty] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      throw Exception('获取专长统计失败: $e');
    }
  }

  // 获取地区统计
  static Future<Map<String, int>> getCityStats() async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('coaches')
          .where('status', isEqualTo: 'active')
          .get();

      Map<String, int> stats = {};
      for (var doc in snapshot.docs) {
        final coach = Coach.fromFirestore(doc);
        stats[coach.location.city] = (stats[coach.location.city] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      throw Exception('获取地区统计失败: $e');
    }
  }
}
