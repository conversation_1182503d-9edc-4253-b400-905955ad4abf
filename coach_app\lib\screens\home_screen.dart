import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/coach_provider.dart';
import '../widgets/coach_card.dart';
import '../widgets/filter_widget.dart';
import '../utils/constants.dart';
import 'coach_detail_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    
    // 初始加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CoachProvider>().loadCoaches(refresh: true);
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // 滚动到底部，加载更多
      context.read<CoachProvider>().loadMoreCoaches();
    }
  }

  void _performSearch(String value) {
    context.read<CoachProvider>().searchCoaches(value);
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => FilterWidget(
        onApplyFilters: (specialty, city) {
          context.read<CoachProvider>().applyFilters(
            specialty: specialty,
            city: city,
            keyword: _searchController.text.trim().isEmpty 
                ? null 
                : _searchController.text.trim(),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: AppSizes.iconXLarge * 2,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          Text(
            AppStrings.noData,
            style: AppTextStyles.headline3.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          Text(
            '尝试调整搜索条件或筛选器',
            style: AppTextStyles.bodyText2.copyWith(
              color: Colors.grey[500],
            ),
          ),
          const SizedBox(height: AppSizes.paddingLarge),
          ElevatedButton(
            onPressed: () {
              _searchController.clear();
              context.read<CoachProvider>().clearFilters();
            },
            child: const Text('清除筛选条件'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreIndicator(CoachProvider coachProvider) {
    if (!coachProvider.hasMore) {
      return const Padding(
        padding: EdgeInsets.all(AppSizes.paddingMedium),
        child: Center(
          child: Text(
            '没有更多数据了',
            style: AppTextStyles.caption,
          ),
        ),
      );
    }

    if (coachProvider.isLoading) {
      return const Padding(
        padding: EdgeInsets.all(AppSizes.paddingMedium),
        child: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.findCoach),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Container(
            padding: const EdgeInsets.all(AppSizes.paddingMedium),
            color: AppColors.surface,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: AppStrings.search,
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          _performSearch('');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppSizes.radiusLarge),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: AppColors.background,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingMedium,
                  vertical: AppSizes.paddingSmall,
                ),
              ),
              onChanged: _performSearch,
              onSubmitted: _performSearch,
            ),
          ),
          
          // 筛选条件显示
          Consumer<CoachProvider>(
            builder: (context, coachProvider, child) {
              final hasFilters = coachProvider.selectedSpecialty != null ||
                  coachProvider.selectedCity != null ||
                  coachProvider.keyword != null;

              if (!hasFilters) return const SizedBox.shrink();

              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingMedium,
                  vertical: AppSizes.paddingSmall,
                ),
                color: AppColors.primary.withOpacity(0.1),
                child: Row(
                  children: [
                    const Icon(
                      Icons.filter_alt,
                      size: AppSizes.iconSmall,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: AppSizes.paddingSmall),
                    Expanded(
                      child: Text(
                        '筛选条件: ${[
                          if (coachProvider.selectedSpecialty != null)
                            coachProvider.selectedSpecialty!,
                          if (coachProvider.selectedCity != null)
                            coachProvider.selectedCity!,
                          if (coachProvider.keyword != null)
                            '"${coachProvider.keyword!}"',
                        ].join(', ')}',
                        style: AppTextStyles.bodyText2.copyWith(
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        _searchController.clear();
                        coachProvider.clearFilters();
                      },
                      child: const Text('清除'),
                    ),
                  ],
                ),
              );
            },
          ),

          // 教练列表
          Expanded(
            child: Consumer<CoachProvider>(
              builder: (context, coachProvider, child) {
                if (coachProvider.isLoading && coachProvider.coaches.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (coachProvider.errorMessage != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: AppSizes.iconXLarge,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),
                        Text(
                          coachProvider.errorMessage!,
                          style: AppTextStyles.bodyText1.copyWith(
                            color: AppColors.error,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppSizes.paddingMedium),
                        ElevatedButton(
                          onPressed: () {
                            coachProvider.clearError();
                            coachProvider.refreshCoaches();
                          },
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  );
                }

                if (coachProvider.coaches.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () => coachProvider.refreshCoaches(),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(AppSizes.paddingMedium),
                    itemCount: coachProvider.coaches.length + 1,
                    itemBuilder: (context, index) {
                      if (index == coachProvider.coaches.length) {
                        return _buildLoadMoreIndicator(coachProvider);
                      }

                      final coach = coachProvider.coaches[index];
                      return CoachCard(
                        coach: coach,
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CoachDetailScreen(coach: coach),
                            ),
                          );
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
