import 'package:flutter/material.dart';
import '../models/coach.dart';
import '../utils/constants.dart';

class LocationPicker extends StatefulWidget {
  final Location? initialLocation;
  final Function(Location) onLocationSelected;

  const LocationPicker({
    super.key,
    this.initialLocation,
    required this.onLocationSelected,
  });

  @override
  State<LocationPicker> createState() => _LocationPickerState();
}

class _LocationPickerState extends State<LocationPicker> {
  String? _selectedProvince;
  String? _selectedCity;
  String? _selectedDistrict;

  // 简化的省市区数据
  final Map<String, Map<String, List<String>>> _locationData = {
    '北京市': {
      '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区']
    },
    '上海市': {
      '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区']
    },
    '广东省': {
      '广州市': ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
      '深圳市': ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区'],
      '珠海市': ['香洲区', '斗门区', '金湾区'],
      '佛山市': ['禅城区', '南海区', '顺德区', '三水区', '高明区'],
    },
    '江苏省': {
      '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],
      '苏州市': ['虎丘区', '吴中区', '相城区', '姑苏区', '吴江区', '常熟市', '张家港市', '昆山市', '太仓市'],
      '无锡市': ['锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市'],
    },
    '浙江省': {
      '杭州市': ['上城区', '下城区', '江干区', '拱墅区', '西湖区', '滨江区', '萧山区', '余杭区', '富阳区', '临安区', '桐庐县', '淳安县', '建德市'],
      '宁波市': ['海曙区', '江北区', '北仑区', '镇海区', '鄞州区', '奉化区', '象山县', '宁海县', '余姚市', '慈溪市'],
    },
  };

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _selectedProvince = widget.initialLocation!.province;
      _selectedCity = widget.initialLocation!.city;
      _selectedDistrict = widget.initialLocation!.district;
    }
  }

  void _onProvinceChanged(String? province) {
    setState(() {
      _selectedProvince = province;
      _selectedCity = null;
      _selectedDistrict = null;
    });
  }

  void _onCityChanged(String? city) {
    setState(() {
      _selectedCity = city;
      _selectedDistrict = null;
    });
  }

  void _onDistrictChanged(String? district) {
    setState(() {
      _selectedDistrict = district;
    });
    
    if (_selectedProvince != null && _selectedCity != null && district != null) {
      final location = Location(
        province: _selectedProvince!,
        city: _selectedCity!,
        district: district,
      );
      widget.onLocationSelected(location);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 省份选择
          DropdownButtonFormField<String>(
            value: _selectedProvince,
            decoration: const InputDecoration(
              labelText: '省份',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppSizes.paddingSmall,
                vertical: AppSizes.paddingSmall,
              ),
            ),
            items: _locationData.keys.map((province) {
              return DropdownMenuItem(
                value: province,
                child: Text(province),
              );
            }).toList(),
            onChanged: _onProvinceChanged,
          ),
          const SizedBox(height: AppSizes.paddingMedium),

          // 城市选择
          DropdownButtonFormField<String>(
            value: _selectedCity,
            decoration: const InputDecoration(
              labelText: '城市',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppSizes.paddingSmall,
                vertical: AppSizes.paddingSmall,
              ),
            ),
            items: _selectedProvince != null
                ? _locationData[_selectedProvince]!.keys.map((city) {
                    return DropdownMenuItem(
                      value: city,
                      child: Text(city),
                    );
                  }).toList()
                : [],
            onChanged: _selectedProvince != null ? _onCityChanged : null,
          ),
          const SizedBox(height: AppSizes.paddingMedium),

          // 区县选择
          DropdownButtonFormField<String>(
            value: _selectedDistrict,
            decoration: const InputDecoration(
              labelText: '区县',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppSizes.paddingSmall,
                vertical: AppSizes.paddingSmall,
              ),
            ),
            items: _selectedProvince != null && _selectedCity != null
                ? _locationData[_selectedProvince]![_selectedCity]!.map((district) {
                    return DropdownMenuItem(
                      value: district,
                      child: Text(district),
                    );
                  }).toList()
                : [],
            onChanged: _selectedProvince != null && _selectedCity != null 
                ? _onDistrictChanged 
                : null,
          ),

          // 显示选中的完整地址
          if (_selectedProvince != null && _selectedCity != null && _selectedDistrict != null) ...[
            const SizedBox(height: AppSizes.paddingMedium),
            Container(
              padding: const EdgeInsets.all(AppSizes.paddingSmall),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: AppSizes.iconSmall,
                    color: AppColors.primary,
                  ),
                  const SizedBox(width: AppSizes.paddingSmall),
                  Expanded(
                    child: Text(
                      '$_selectedProvince $_selectedCity $_selectedDistrict',
                      style: AppTextStyles.bodyText2.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
