import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../providers/auth_provider.dart';
import '../providers/coach_provider.dart';
import '../models/coach.dart';
import '../models/user.dart';
import '../services/firebase_service.dart';
import '../widgets/location_picker.dart';
import '../utils/constants.dart';

class UploadScreen extends StatefulWidget {
  const UploadScreen({super.key});

  @override
  State<UploadScreen> createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _contactController = TextEditingController();
  
  String? _selectedSpecialty;
  String? _selectedExperience;
  String? _selectedPriceRange;
  Location? _selectedLocation;
  File? _selectedImage;
  bool _isUploading = false;

  final ImagePicker _picker = ImagePicker();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 80,
      );
      
      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      _showErrorSnackBar('选择图片失败: $e');
    }
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;
    
    final authProvider = context.read<AuthProvider>();
    if (!authProvider.isLoggedIn) {
      _showErrorSnackBar('请先登录');
      return;
    }

    if (_selectedSpecialty == null) {
      _showErrorSnackBar('请选择专长领域');
      return;
    }

    if (_selectedExperience == null) {
      _showErrorSnackBar('请选择从业经验');
      return;
    }

    if (_selectedPriceRange == null) {
      _showErrorSnackBar('请选择价格区间');
      return;
    }

    if (_selectedLocation == null) {
      _showErrorSnackBar('请选择所在地区');
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      String? photoUrl;
      
      // 上传图片
      if (_selectedImage != null) {
        final fileName = 'coaches/${authProvider.user!.id}/${DateTime.now().millisecondsSinceEpoch}.jpg';
        photoUrl = await FirebaseService.uploadFile(_selectedImage!, fileName);
      }

      // 创建教练信息
      final coach = Coach(
        id: '', // Firestore会自动生成
        name: _nameController.text.trim(),
        specialty: _selectedSpecialty!,
        description: _descriptionController.text.trim(),
        photoUrl: photoUrl,
        location: _selectedLocation!,
        contact: _contactController.text.trim(),
        experience: _selectedExperience!,
        priceRange: _selectedPriceRange!,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: 'active',
        userId: authProvider.user!.id,
      );

      final success = await context.read<CoachProvider>().createCoach(coach);
      
      if (success) {
        // 更新用户角色为教练
        await authProvider.updateUserProfile(role: UserRole.coach);
        
        _showSuccessSnackBar('教练信息发布成功！');
        _clearForm();
      } else {
        _showErrorSnackBar('发布失败，请重试');
      }
    } catch (e) {
      _showErrorSnackBar('发布失败: $e');
    } finally {
      setState(() {
        _isUploading = false;
      });
    }
  }

  void _clearForm() {
    _nameController.clear();
    _descriptionController.clear();
    _contactController.clear();
    setState(() {
      _selectedSpecialty = null;
      _selectedExperience = null;
      _selectedPriceRange = null;
      _selectedLocation = null;
      _selectedImage = null;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('发布教练信息'),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          TextButton(
            onPressed: _isUploading ? null : _submitForm,
            child: Text(
              '发布',
              style: TextStyle(
                color: _isUploading ? Colors.grey : AppColors.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isLoggedIn) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.login,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    '请先登录后再发布教练信息',
                    style: AppTextStyles.headline3,
                  ),
                ],
              ),
            );
          }

          return Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSizes.paddingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 头像上传
                  _buildImagePicker(),
                  const SizedBox(height: AppSizes.paddingLarge),
                  
                  // 基本信息
                  _buildBasicInfoForm(),
                  const SizedBox(height: AppSizes.paddingLarge),
                  
                  // 专业信息
                  _buildProfessionalInfo(),
                  const SizedBox(height: AppSizes.paddingLarge),
                  
                  // 地址选择
                  _buildLocationPicker(),
                  const SizedBox(height: AppSizes.paddingXLarge),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildImagePicker() {
    return Center(
      child: Column(
        children: [
          GestureDetector(
            onTap: _pickImage,
            child: Container(
              width: AppSizes.avatarXLarge,
              height: AppSizes.avatarXLarge,
              decoration: BoxDecoration(
                color: AppColors.background,
                shape: BoxShape.circle,
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: _selectedImage != null
                  ? ClipOval(
                      child: Image.file(
                        _selectedImage!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Icon(
                      Icons.add_a_photo,
                      size: AppSizes.iconLarge,
                      color: Colors.grey[600],
                    ),
            ),
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          Text(
            '点击上传头像',
            style: AppTextStyles.bodyText2.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '基本信息',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: '姓名',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入姓名';
            }
            return null;
          },
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        TextFormField(
          controller: _contactController,
          decoration: const InputDecoration(
            labelText: '联系电话',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入联系电话';
            }
            return null;
          },
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        TextFormField(
          controller: _descriptionController,
          decoration: const InputDecoration(
            labelText: '个人简介',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          maxLines: 4,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入个人简介';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildProfessionalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '专业信息',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        // 专长选择
        DropdownButtonFormField<String>(
          value: _selectedSpecialty,
          decoration: const InputDecoration(
            labelText: '专长领域',
            border: OutlineInputBorder(),
          ),
          items: AppConstants.specialties.map((specialty) {
            return DropdownMenuItem(
              value: specialty,
              child: Text(specialty),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedSpecialty = value;
            });
          },
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        // 经验选择
        DropdownButtonFormField<String>(
          value: _selectedExperience,
          decoration: const InputDecoration(
            labelText: '从业经验',
            border: OutlineInputBorder(),
          ),
          items: AppConstants.experienceRanges.map((experience) {
            return DropdownMenuItem(
              value: experience,
              child: Text(experience),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedExperience = value;
            });
          },
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        // 价格选择
        DropdownButtonFormField<String>(
          value: _selectedPriceRange,
          decoration: const InputDecoration(
            labelText: '价格区间',
            border: OutlineInputBorder(),
          ),
          items: AppConstants.priceRanges.map((price) {
            return DropdownMenuItem(
              value: price,
              child: Text(price),
            );
          }).toList(),
          onChanged: (value) {
            setState(() {
              _selectedPriceRange = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildLocationPicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '所在地区',
          style: AppTextStyles.headline3,
        ),
        const SizedBox(height: AppSizes.paddingMedium),
        
        LocationPicker(
          initialLocation: _selectedLocation,
          onLocationSelected: (location) {
            setState(() {
              _selectedLocation = location;
            });
          },
        ),
      ],
    );
  }
}
