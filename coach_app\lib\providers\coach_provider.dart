import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/coach.dart';
import '../services/firebase_service.dart';
import '../utils/constants.dart';

class CoachProvider extends ChangeNotifier {
  List<Coach> _coaches = [];
  List<Coach> _myCoaches = [];
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  DocumentSnapshot? _lastDocument;

  // 筛选条件
  String? _selectedSpecialty;
  String? _selectedCity;
  String? _keyword;

  List<Coach> get coaches => _coaches;
  List<Coach> get myCoaches => _myCoaches;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get errorMessage => _errorMessage;
  String? get selectedSpecialty => _selectedSpecialty;
  String? get selectedCity => _selectedCity;
  String? get keyword => _keyword;

  Future<void> loadCoaches({bool refresh = false}) async {
    if (_isLoading) return;

    try {
      _isLoading = true;
      if (refresh) {
        _coaches.clear();
        _lastDocument = null;
        _hasMore = true;
      }
      _errorMessage = null;
      notifyListeners();

      final coaches = await FirebaseService.getCoaches(
        limit: AppConstants.pageSize,
        lastDocument: _lastDocument,
        specialty: _selectedSpecialty,
        city: _selectedCity,
        keyword: _keyword,
      );

      if (coaches.isNotEmpty) {
        if (refresh) {
          _coaches = coaches;
        } else {
          _coaches.addAll(coaches);
        }
        
        // 更新最后一个文档引用（用于分页）
        if (coaches.length == AppConstants.pageSize) {
          // 这里需要从Firestore获取DocumentSnapshot
          // 实际实现中需要修改FirebaseService.getCoaches方法返回DocumentSnapshot
          _hasMore = true;
        } else {
          _hasMore = false;
        }
      } else {
        _hasMore = false;
      }
    } catch (e) {
      _errorMessage = '加载教练列表失败: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> refreshCoaches() async {
    await loadCoaches(refresh: true);
  }

  Future<void> loadMoreCoaches() async {
    if (!_hasMore || _isLoading) return;
    await loadCoaches(refresh: false);
  }

  Future<void> loadMyCoaches(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      _myCoaches = await FirebaseService.getCoachesByUserId(userId);
    } catch (e) {
      _errorMessage = '加载我的教练信息失败: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> createCoach(Coach coach) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await FirebaseService.createCoach(coach);
      
      // 添加到本地列表
      _coaches.insert(0, coach);
      _myCoaches.insert(0, coach);
      
      return true;
    } catch (e) {
      _errorMessage = '创建教练信息失败: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateCoach(Coach coach) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await FirebaseService.updateCoach(coach);
      
      // 更新本地列表
      final index = _coaches.indexWhere((c) => c.id == coach.id);
      if (index != -1) {
        _coaches[index] = coach;
      }
      
      final myIndex = _myCoaches.indexWhere((c) => c.id == coach.id);
      if (myIndex != -1) {
        _myCoaches[myIndex] = coach;
      }
      
      return true;
    } catch (e) {
      _errorMessage = '更新教练信息失败: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> deleteCoach(String coachId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await FirebaseService.deleteCoach(coachId);
      
      // 从本地列表移除
      _coaches.removeWhere((c) => c.id == coachId);
      _myCoaches.removeWhere((c) => c.id == coachId);
      
      return true;
    } catch (e) {
      _errorMessage = '删除教练信息失败: $e';
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void applyFilters({
    String? specialty,
    String? city,
    String? keyword,
  }) {
    _selectedSpecialty = specialty;
    _selectedCity = city;
    _keyword = keyword;
    
    // 重新加载数据
    loadCoaches(refresh: true);
  }

  void clearFilters() {
    _selectedSpecialty = null;
    _selectedCity = null;
    _keyword = null;
    
    // 重新加载数据
    loadCoaches(refresh: true);
  }

  void searchCoaches(String keyword) {
    _keyword = keyword.trim().isEmpty ? null : keyword.trim();
    loadCoaches(refresh: true);
  }

  Coach? getCoachById(String coachId) {
    try {
      return _coaches.firstWhere((coach) => coach.id == coachId);
    } catch (e) {
      return null;
    }
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // 获取筛选统计信息
  Future<Map<String, int>> getSpecialtyStats() async {
    try {
      return await FirebaseService.getSpecialtyStats();
    } catch (e) {
      return {};
    }
  }

  Future<Map<String, int>> getCityStats() async {
    try {
      return await FirebaseService.getCityStats();
    } catch (e) {
      return {};
    }
  }
}
