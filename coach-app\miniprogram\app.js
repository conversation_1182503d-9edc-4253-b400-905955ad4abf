// app.js
App({
  onLaunch() {
    // 初始化云开发
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        // env 参数说明：
        //   env 参数决定接下来小程序发起的云开发调用（wx.cloud.xxx）会默认请求到哪个云环境的资源
        //   此处请填入环境 ID, 环境 ID 可打开云控制台查看
        //   如不填则使用默认环境（第一个创建的环境）
        env: 'your-env-id', // 请替换为你的云环境ID
        traceUser: true,
      });
    }

    // 检查登录状态
    this.checkLoginStatus();
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(msg) {
    console.log('小程序错误:', msg);
  },

  // 检查用户登录状态
  checkLoginStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.globalData.userInfo = userInfo;
      this.globalData.isLoggedIn = true;
    } else {
      this.globalData.isLoggedIn = false;
    }
  },

  // 用户登录
  login() {
    return new Promise((resolve, reject) => {
      wx.cloud.callFunction({
        name: 'login',
        success: res => {
          console.log('登录成功', res);
          this.globalData.userInfo = res.result.userInfo;
          this.globalData.isLoggedIn = true;
          wx.setStorageSync('userInfo', res.result.userInfo);
          resolve(res.result);
        },
        fail: err => {
          console.error('登录失败', err);
          reject(err);
        }
      });
    });
  },

  // 用户登出
  logout() {
    this.globalData.userInfo = null;
    this.globalData.isLoggedIn = false;
    wx.removeStorageSync('userInfo');
  },

  // 全局数据
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    version: '1.0.0'
  }
});
