import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../providers/auth_provider.dart';
import '../providers/coach_provider.dart';
import '../models/user.dart';
import '../utils/constants.dart';
import 'login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final authProvider = context.read<AuthProvider>();
    if (authProvider.isLoggedIn && authProvider.user!.isCoach) {
      context.read<CoachProvider>().loadMyCoaches(authProvider.user!.id);
    }
  }

  void _showLoginDialog() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('确定要退出登录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthProvider>().signOut();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppStrings.profile),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isLoggedIn) {
            return _buildLoginPrompt();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // 用户信息卡片
                _buildUserInfoCard(authProvider.user!),
                const SizedBox(height: AppSizes.paddingMedium),
                
                // 我的发布（仅教练可见）
                if (authProvider.user!.isCoach) ...[
                  _buildMyCoachesSection(),
                  const SizedBox(height: AppSizes.paddingMedium),
                ],
                
                // 功能菜单
                _buildMenuList(authProvider),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoginPrompt() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingLarge),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_outline,
              size: AppSizes.iconXLarge * 2,
              color: Colors.grey[400],
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            Text(
              '登录后查看更多功能',
              style: AppTextStyles.headline3.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: AppSizes.paddingSmall),
            Text(
              '登录后可以发布教练信息、管理个人资料等',
              style: AppTextStyles.bodyText2.copyWith(
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.paddingLarge),
            ElevatedButton(
              onPressed: _showLoginDialog,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.onPrimary,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppSizes.paddingLarge,
                  vertical: AppSizes.paddingMedium,
                ),
              ),
              child: const Text('立即登录'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoCard(AppUser user) {
    return Container(
      margin: const EdgeInsets.all(AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: AppSizes.avatarMedium / 2,
            backgroundColor: AppColors.background,
            backgroundImage: user.avatarUrl != null
                ? CachedNetworkImageProvider(user.avatarUrl!)
                : null,
            child: user.avatarUrl == null
                ? Icon(
                    Icons.person,
                    size: AppSizes.iconLarge,
                    color: Colors.grey[600],
                  )
                : null,
          ),
          const SizedBox(width: AppSizes.paddingMedium),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: AppTextStyles.headline3,
                ),
                const SizedBox(height: AppSizes.paddingSmall),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingSmall,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: user.isCoach 
                        ? AppColors.primary.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: Text(
                    user.isCoach ? '教练' : '用户',
                    style: AppTextStyles.caption.copyWith(
                      color: user.isCoach ? AppColors.primary : Colors.grey[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: AppSizes.paddingSmall),
                Text(
                  user.email,
                  style: AppTextStyles.bodyText2.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyCoachesSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppSizes.paddingSmall),
            child: Text(
              '我的发布',
              style: AppTextStyles.headline3,
            ),
          ),
          const SizedBox(height: AppSizes.paddingSmall),
          
          Consumer<CoachProvider>(
            builder: (context, coachProvider, child) {
              if (coachProvider.isLoading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (coachProvider.myCoaches.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(AppSizes.paddingLarge),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
                  ),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.add_circle_outline,
                          size: AppSizes.iconLarge,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: AppSizes.paddingSmall),
                        Text(
                          '还没有发布教练信息',
                          style: AppTextStyles.bodyText2.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: coachProvider.myCoaches.length,
                itemBuilder: (context, index) {
                  final coach = coachProvider.myCoaches[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundImage: coach.photoUrl != null
                            ? CachedNetworkImageProvider(coach.photoUrl!)
                            : null,
                        child: coach.photoUrl == null
                            ? const Icon(Icons.person)
                            : null,
                      ),
                      title: Text(coach.name),
                      subtitle: Text(coach.specialty),
                      trailing: PopupMenuButton(
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Text('编辑'),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Text('删除'),
                          ),
                        ],
                        onSelected: (value) {
                          if (value == 'edit') {
                            // TODO: 实现编辑功能
                          } else if (value == 'delete') {
                            // TODO: 实现删除功能
                          }
                        },
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMenuList(AuthProvider authProvider) {
    final menuItems = [
      {
        'icon': Icons.edit,
        'title': '编辑资料',
        'onTap': () {
          // TODO: 实现编辑资料功能
        },
      },
      {
        'icon': Icons.settings,
        'title': '设置',
        'onTap': () {
          // TODO: 实现设置功能
        },
      },
      {
        'icon': Icons.help_outline,
        'title': '帮助与反馈',
        'onTap': () {
          // TODO: 实现帮助功能
        },
      },
      {
        'icon': Icons.info_outline,
        'title': '关于我们',
        'onTap': () {
          // TODO: 实现关于页面
        },
      },
      {
        'icon': Icons.logout,
        'title': '退出登录',
        'onTap': _showLogoutDialog,
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: menuItems.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final item = menuItems[index];
          return ListTile(
            leading: Icon(
              item['icon'] as IconData,
              color: AppColors.primary,
            ),
            title: Text(item['title'] as String),
            trailing: const Icon(Icons.chevron_right),
            onTap: item['onTap'] as VoidCallback,
          );
        },
      ),
    );
  }
}
