import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/coach.dart';
import '../utils/constants.dart';

class CoachCard extends StatelessWidget {
  final Coach coach;
  final VoidCallback? onTap;

  const CoachCard({
    super.key,
    required this.coach,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingMedium),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppSizes.paddingMedium),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头像
              Hero(
                tag: 'coach_avatar_${coach.id}',
                child: CircleAvatar(
                  radius: AppSizes.avatarMedium / 2,
                  backgroundColor: AppColors.background,
                  backgroundImage: coach.photoUrl != null
                      ? CachedNetworkImageProvider(coach.photoUrl!)
                      : null,
                  child: coach.photoUrl == null
                      ? Icon(
                          Icons.person,
                          size: AppSizes.iconMedium,
                          color: Colors.grey[600],
                        )
                      : null,
                ),
              ),
              const SizedBox(width: AppSizes.paddingMedium),
              
              // 教练信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 姓名和专长
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            coach.name,
                            style: AppTextStyles.headline3,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSizes.paddingSmall,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                          ),
                          child: Text(
                            coach.specialty,
                            style: AppTextStyles.caption.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingSmall),
                    
                    // 个人简介
                    Text(
                      coach.description.isNotEmpty 
                          ? coach.description 
                          : '暂无个人简介',
                      style: AppTextStyles.bodyText2.copyWith(
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppSizes.paddingSmall),
                    
                    // 经验和价格
                    Row(
                      children: [
                        Icon(
                          Icons.work_outline,
                          size: AppSizes.iconSmall,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          coach.experience,
                          style: AppTextStyles.caption.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: AppSizes.paddingMedium),
                        Icon(
                          Icons.attach_money,
                          size: AppSizes.iconSmall,
                          color: Colors.grey[500],
                        ),
                        Expanded(
                          child: Text(
                            coach.priceRange,
                            style: AppTextStyles.caption.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: AppSizes.paddingSmall),
                    
                    // 位置信息
                    Row(
                      children: [
                        Icon(
                          Icons.location_on_outlined,
                          size: AppSizes.iconSmall,
                          color: Colors.grey[500],
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            coach.location.fullAddress,
                            style: AppTextStyles.caption.copyWith(
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 箭头图标
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
                size: AppSizes.iconMedium,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
