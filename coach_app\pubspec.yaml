name: coach_app
description: 教练查找与数据上传应用

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # Firebase
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
  firebase_storage: ^11.2.5

  # State Management
  provider: ^6.0.5

  # UI & Images
  cached_network_image: ^3.2.3
  image_picker: ^1.0.2

  # Network & Storage
  dio: ^5.3.0
  shared_preferences: ^2.2.0
  url_launcher: ^6.1.12

  # Permissions
  permission_handler: ^10.4.3

  # Utils
  intl: ^0.18.1

  # Icons
  cupertino_icons: ^1.0.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/data/

  # Fonts (可选)
  # fonts:
  #   - family: <PERSON>hyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
