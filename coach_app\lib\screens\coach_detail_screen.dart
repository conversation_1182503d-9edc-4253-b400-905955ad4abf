import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/coach.dart';
import '../utils/constants.dart';

class CoachDetailScreen extends StatelessWidget {
  final Coach coach;

  const CoachDetailScreen({
    super.key,
    required this.coach,
  });

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    }
  }

  Future<void> _shareCoach() async {
    // TODO: 实现分享功能
    // 可以使用 share_plus 插件
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(coach.name),
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: _shareCoach,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 教练头像和基本信息
            _buildCoachHeader(),
            
            // 专长和经验
            _buildSpecialtySection(),
            
            // 详细描述
            _buildDescriptionSection(),
            
            // 联系方式
            _buildContactSection(),
            
            // 位置信息
            _buildLocationSection(),
          ],
        ),
      ),
      bottomNavigationBar: _buildContactButton(),
    );
  }

  Widget _buildCoachHeader() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingLarge),
      color: AppColors.surface,
      child: Row(
        children: [
          // 头像
          Hero(
            tag: 'coach_avatar_${coach.id}',
            child: CircleAvatar(
              radius: AppSizes.avatarLarge / 2,
              backgroundColor: AppColors.background,
              backgroundImage: coach.photoUrl != null
                  ? CachedNetworkImageProvider(coach.photoUrl!)
                  : null,
              child: coach.photoUrl == null
                  ? Icon(
                      Icons.person,
                      size: AppSizes.iconLarge,
                      color: Colors.grey[600],
                    )
                  : null,
            ),
          ),
          const SizedBox(width: AppSizes.paddingMedium),
          
          // 基本信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  coach.name,
                  style: AppTextStyles.headline2,
                ),
                const SizedBox(height: AppSizes.paddingSmall),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSizes.paddingSmall,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
                  ),
                  child: Text(
                    coach.specialty,
                    style: AppTextStyles.bodyText2.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(height: AppSizes.paddingSmall),
                Row(
                  children: [
                    Icon(
                      Icons.work_outline,
                      size: AppSizes.iconSmall,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '经验: ${coach.experience}',
                      style: AppTextStyles.bodyText2.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpecialtySection() {
    return Container(
      margin: const EdgeInsets.all(AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star_outline,
                color: AppColors.primary,
                size: AppSizes.iconMedium,
              ),
              const SizedBox(width: AppSizes.paddingSmall),
              Text(
                '专业信息',
                style: AppTextStyles.headline3,
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          
          _buildInfoRow('专长领域', coach.specialty),
          _buildInfoRow('从业经验', coach.experience),
          _buildInfoRow('价格区间', coach.priceRange),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description_outlined,
                color: AppColors.primary,
                size: AppSizes.iconMedium,
              ),
              const SizedBox(width: AppSizes.paddingSmall),
              Text(
                '个人简介',
                style: AppTextStyles.headline3,
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          Text(
            coach.description.isNotEmpty 
                ? coach.description 
                : '暂无个人简介',
            style: AppTextStyles.bodyText1,
            textAlign: TextAlign.justify,
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection() {
    return Container(
      margin: const EdgeInsets.all(AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.contact_phone_outlined,
                color: AppColors.primary,
                size: AppSizes.iconMedium,
              ),
              const SizedBox(width: AppSizes.paddingSmall),
              Text(
                '联系方式',
                style: AppTextStyles.headline3,
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          
          GestureDetector(
            onTap: () => _makePhoneCall(coach.contact),
            child: Container(
              padding: const EdgeInsets.all(AppSizes.paddingSmall),
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(AppSizes.radiusSmall),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.phone,
                    color: AppColors.primary,
                    size: AppSizes.iconSmall,
                  ),
                  const SizedBox(width: AppSizes.paddingSmall),
                  Text(
                    coach.contact,
                    style: AppTextStyles.bodyText1.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.call,
                    color: AppColors.primary,
                    size: AppSizes.iconSmall,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppSizes.paddingMedium),
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on_outlined,
                color: AppColors.primary,
                size: AppSizes.iconMedium,
              ),
              const SizedBox(width: AppSizes.paddingSmall),
              Text(
                '所在地区',
                style: AppTextStyles.headline3,
              ),
            ],
          ),
          const SizedBox(height: AppSizes.paddingMedium),
          Text(
            coach.location.fullAddress,
            style: AppTextStyles.bodyText1,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppSizes.paddingSmall),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: AppTextStyles.bodyText2.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyText1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactButton() {
    return Container(
      padding: const EdgeInsets.all(AppSizes.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: ElevatedButton(
          onPressed: () => _makePhoneCall(coach.contact),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingMedium),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppSizes.radiusMedium),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.phone),
              const SizedBox(width: AppSizes.paddingSmall),
              Text(
                '立即联系',
                style: AppTextStyles.headline3.copyWith(
                  color: AppColors.onPrimary,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
