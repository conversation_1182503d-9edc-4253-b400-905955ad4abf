# 教练应用详细实施指南 (Flutter版本)

## 项目概述
基于Flutter和Firebase的教练查找与数据上传MVP应用，实现跨平台开发和现代化的用户体验。

## 技术栈选择
- **前端**: Flutter (Dart语言)
- **后端**: Firebase (Firestore + Cloud Storage + Authentication)
- **状态管理**: Provider / Riverpod
- **网络请求**: Dio / HTTP
- **身份验证**: Firebase Authentication (支持多种登录方式)

## 第一阶段：项目初始化与环境搭建

### 1.1 开发环境准备
- [ ] 安装Flutter SDK
- [ ] 安装Android Studio / VS Code
- [ ] 配置Android模拟器或真机调试
- [ ] 创建Firebase项目
- [ ] 配置Firebase Android/iOS应用

### 1.2 项目结构创建
```
coach_app/
├── lib/
│   ├── main.dart              # 应用入口
│   ├── models/               # 数据模型
│   │   ├── coach.dart        # 教练模型
│   │   └── user.dart         # 用户模型
│   ├── screens/              # 页面
│   │   ├── home_screen.dart  # 首页（教练列表）
│   │   ├── coach_detail_screen.dart # 教练详情页
│   │   ├── upload_screen.dart # 教练数据上传页
│   │   └── profile_screen.dart # 个人中心
│   ├── widgets/              # 组件
│   │   ├── coach_card.dart   # 教练卡片组件
│   │   ├── filter_widget.dart # 筛选组件
│   │   └── location_picker.dart # 地址选择器
│   ├── services/             # 服务层
│   │   ├── firebase_service.dart # Firebase服务
│   │   ├── auth_service.dart # 认证服务
│   │   └── storage_service.dart # 存储服务
│   ├── providers/            # 状态管理
│   │   ├── coach_provider.dart # 教练数据状态
│   │   └── auth_provider.dart # 认证状态
│   └── utils/               # 工具函数
│       ├── constants.dart   # 常量
│       └── helpers.dart     # 辅助函数
├── android/                 # Android配置
├── ios/                     # iOS配置
├── assets/                  # 资源文件
│   ├── images/             # 图片资源
│   └── data/               # 静态数据
└── pubspec.yaml            # 依赖配置
```

### 1.3 基础配置
- [ ] 配置pubspec.yaml依赖
- [ ] 设置Firebase配置文件
- [ ] 配置Android权限和网络安全
- [ ] 设置应用图标和启动页

## 第二阶段：后端服务设计与实现

### 2.1 数据模型设计

#### 教练数据模型 (Coach)
```dart
class Coach {
  final String id;
  final String name;
  final String specialty;
  final String description;
  final String? photoUrl;
  final Location location;
  final String contact;
  final int experience;
  final String priceRange;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String status; // active/inactive
  final String userId; // 关联的用户ID

  Coach({
    required this.id,
    required this.name,
    required this.specialty,
    required this.description,
    this.photoUrl,
    required this.location,
    required this.contact,
    required this.experience,
    required this.priceRange,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
    required this.userId,
  });

  factory Coach.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Coach(
      id: doc.id,
      name: data['name'] ?? '',
      specialty: data['specialty'] ?? '',
      description: data['description'] ?? '',
      photoUrl: data['photoUrl'],
      location: Location.fromMap(data['location']),
      contact: data['contact'] ?? '',
      experience: data['experience'] ?? 0,
      priceRange: data['priceRange'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      status: data['status'] ?? 'active',
      userId: data['userId'] ?? '',
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'specialty': specialty,
      'description': description,
      'photoUrl': photoUrl,
      'location': location.toMap(),
      'contact': contact,
      'experience': experience,
      'priceRange': priceRange,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'status': status,
      'userId': userId,
    };
  }
}

class Location {
  final String province;
  final String city;
  final String district;

  Location({
    required this.province,
    required this.city,
    required this.district,
  });

  factory Location.fromMap(Map<String, dynamic> map) {
    return Location(
      province: map['province'] ?? '',
      city: map['city'] ?? '',
      district: map['district'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'province': province,
      'city': city,
      'district': district,
    };
  }
}
```

#### 用户数据模型 (User)
```dart
class AppUser {
  final String id;
  final String name;
  final String email;
  final String? avatarUrl;
  final UserRole role;
  final DateTime createdAt;

  AppUser({
    required this.id,
    required this.name,
    required this.email,
    this.avatarUrl,
    required this.role,
    required this.createdAt,
  });

  factory AppUser.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return AppUser(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      avatarUrl: data['avatarUrl'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == 'UserRole.${data['role']}',
        orElse: () => UserRole.user,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'email': email,
      'avatarUrl': avatarUrl,
      'role': role.toString().split('.').last,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}

enum UserRole { user, coach, admin }
```

### 2.2 Firebase服务设计

#### FirebaseService - 核心数据服务
```dart
class FirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // 获取教练列表
  Future<List<Coach>> getCoaches({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    String? specialty,
    String? city,
    String? keyword,
  }) async {
    Query query = _firestore.collection('coaches')
        .where('status', isEqualTo: 'active')
        .orderBy('createdAt', descending: true);

    if (specialty != null && specialty.isNotEmpty) {
      query = query.where('specialty', isEqualTo: specialty);
    }

    if (city != null && city.isNotEmpty) {
      query = query.where('location.city', isEqualTo: city);
    }

    if (lastDocument != null) {
      query = query.startAfterDocument(lastDocument);
    }

    query = query.limit(limit);

    final QuerySnapshot snapshot = await query.get();
    return snapshot.docs.map((doc) => Coach.fromFirestore(doc)).toList();
  }

  // 上传教练数据
  Future<void> uploadCoach(Coach coach) async {
    await _firestore.collection('coaches').doc(coach.id).set(coach.toFirestore());
  }

  // 上传文件
  Future<String> uploadFile(File file, String path) async {
    final ref = _storage.ref().child(path);
    final uploadTask = await ref.putFile(file);
    return await uploadTask.ref.getDownloadURL();
  }
}
```

### 2.3 Firestore安全规则
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // 教练数据规则
    match /coaches/{coachId} {
      allow read: if true; // 所有人可读
      allow write: if request.auth != null &&
                      request.auth.uid == resource.data.userId;
    }

    // 用户数据规则
    match /users/{userId} {
      allow read, write: if request.auth != null &&
                            request.auth.uid == userId;
    }
  }
}

## 第三阶段：前端核心功能开发

### 3.1 主页面 (HomeScreen)
```dart
class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('找教练'),
        backgroundColor: Colors.blue[700],
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Padding(
            padding: EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索教练...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25.0),
                ),
              ),
              onChanged: (value) => _performSearch(value),
            ),
          ),
          // 教练列表
          Expanded(
            child: Consumer<CoachProvider>(
              builder: (context, coachProvider, child) {
                if (coachProvider.isLoading && coachProvider.coaches.isEmpty) {
                  return Center(child: CircularProgressIndicator());
                }

                if (coachProvider.coaches.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: () => coachProvider.refreshCoaches(),
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount: coachProvider.coaches.length + 1,
                    itemBuilder: (context, index) {
                      if (index == coachProvider.coaches.length) {
                        return _buildLoadMoreIndicator(coachProvider);
                      }
                      return CoachCard(coach: coachProvider.coaches[index]);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

**功能清单:**
- [ ] 实现教练卡片展示 (CoachCard组件)
- [ ] 添加搜索功能
- [ ] 实现筛选器（专长、地区）
- [ ] 支持下拉刷新和上拉加载
- [ ] 添加空状态处理
- [ ] 实现无限滚动加载

### 3.2 教练详情页面 (CoachDetailScreen)
```dart
class CoachDetailScreen extends StatelessWidget {
  final Coach coach;

  const CoachDetailScreen({Key? key, required this.coach}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(coach.name),
        actions: [
          IconButton(
            icon: Icon(Icons.share),
            onPressed: () => _shareCoach(coach),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 教练头像和基本信息
            _buildCoachHeader(),
            SizedBox(height: 20),
            // 专长和经验
            _buildSpecialtySection(),
            SizedBox(height: 20),
            // 详细描述
            _buildDescriptionSection(),
            SizedBox(height: 20),
            // 联系方式
            _buildContactSection(),
            SizedBox(height: 20),
            // 位置信息
            _buildLocationSection(),
          ],
        ),
      ),
      bottomNavigationBar: _buildContactButton(),
    );
  }
}
```

**功能清单:**
- [ ] 展示教练完整信息
- [ ] 图片预览功能 (Hero动画)
- [ ] 联系方式展示和拨打
- [ ] 分享功能实现
- [ ] 响应式布局设计

### 3.3 数据上传页面 (UploadScreen)
```dart
class UploadScreen extends StatefulWidget {
  @override
  _UploadScreenState createState() => _UploadScreenState();
}

class _UploadScreenState extends State<UploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _specialtyController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _contactController = TextEditingController();
  final _priceController = TextEditingController();

  File? _selectedImage;
  Location? _selectedLocation;
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('发布教练信息'),
        actions: [
          TextButton(
            onPressed: _isUploading ? null : _submitForm,
            child: Text('发布', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16.0),
          child: Column(
            children: [
              // 头像上传
              _buildImagePicker(),
              SizedBox(height: 20),
              // 基本信息表单
              _buildBasicInfoForm(),
              SizedBox(height: 20),
              // 地址选择器
              _buildLocationPicker(),
              SizedBox(height: 20),
              // 提交按钮
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }
}
```

**功能清单:**
- [ ] 表单设计和验证 (使用Form和TextFormField)
- [ ] 图片选择和上传 (image_picker插件)
- [ ] 地区选择器 (三级联动)
- [ ] 提交状态处理 (loading状态)
- [ ] 权限验证 (仅教练可发布)
- [ ] 表单数据持久化

### 3.4 个人中心页面 (ProfileScreen)
```dart
class ProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('个人中心'),
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          if (!authProvider.isLoggedIn) {
            return _buildLoginPrompt();
          }

          return SingleChildScrollView(
            child: Column(
              children: [
                // 用户信息卡片
                _buildUserInfoCard(authProvider.user!),
                SizedBox(height: 20),
                // 功能菜单
                _buildMenuList(),
              ],
            ),
          );
        },
      ),
    );
  }
}
```

**功能清单:**
- [ ] 用户信息展示
- [ ] 登录/注册功能
- [ ] 角色切换功能 (用户/教练)
- [ ] 我的发布管理（教练专用）
- [ ] 设置和帮助页面
- [ ] 退出登录功能

## 第四阶段：地理位置筛选功能

### 4.1 静态地址数据
```dart
// assets/data/china_regions.json
{
  "provinces": [
    {
      "name": "北京市",
      "cities": [
        {
          "name": "北京市",
          "districts": ["东城区", "西城区", "朝阳区", "丰台区", "石景山区"]
        }
      ]
    },
    {
      "name": "上海市",
      "cities": [
        {
          "name": "上海市",
          "districts": ["黄浦区", "徐汇区", "长宁区", "静安区", "普陀区"]
        }
      ]
    }
  ]
}
```

```dart
// 地址选择器组件
class LocationPicker extends StatefulWidget {
  final Function(Location) onLocationSelected;
  final Location? initialLocation;

  const LocationPicker({
    Key? key,
    required this.onLocationSelected,
    this.initialLocation,
  }) : super(key: key);

  @override
  _LocationPickerState createState() => _LocationPickerState();
}
```

**功能清单:**
- [ ] 准备省市区三级联动数据 (JSON格式)
- [ ] 创建地址选择组件 (CascadingDropdown)
- [ ] 实现筛选逻辑 (多条件查询)
- [ ] 地址数据本地缓存

### 4.2 筛选功能实现
```dart
class FilterProvider extends ChangeNotifier {
  String? _selectedSpecialty;
  Location? _selectedLocation;
  String? _keyword;
  List<String> _filterHistory = [];

  // 应用筛选条件
  void applyFilters({
    String? specialty,
    Location? location,
    String? keyword,
  }) {
    _selectedSpecialty = specialty;
    _selectedLocation = location;
    _keyword = keyword;

    // 保存筛选历史
    _saveFilterHistory();
    notifyListeners();
  }

  // 清除筛选条件
  void clearFilters() {
    _selectedSpecialty = null;
    _selectedLocation = null;
    _keyword = null;
    notifyListeners();
  }
}
```

**功能清单:**
- [ ] 多条件组合筛选 (专长+地区+关键词)
- [ ] 筛选结果缓存 (SharedPreferences)
- [ ] 筛选历史记录
- [ ] 热门筛选条件推荐

## 第五阶段：应用打包与合规准备

### 5.1 Flutter应用打包
```bash
# Android打包
flutter build apk --release
flutter build appbundle --release

# iOS打包 (需要Mac环境)
flutter build ios --release
```

**配置清单:**
- [ ] 配置android/app/build.gradle签名
- [ ] 设置应用版本号和构建号
- [ ] 配置ProGuard混淆 (可选)
- [ ] 生成APK/AAB安装包
- [ ] 测试安装和运行

### 5.2 合规性准备
```dart
// 隐私政策弹窗
class PrivacyDialog extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('隐私政策'),
      content: SingleChildScrollView(
        child: Text('我们重视您的隐私...'),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text('拒绝'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(true),
          child: Text('同意'),
        ),
      ],
    );
  }
}
```

**合规清单:**
- [ ] 申请软件著作权
- [ ] 准备APP备案材料
- [ ] 编写隐私政策文档
- [ ] 实现隐私授权弹窗
- [ ] 配置权限申请说明

### 5.3 应用商店准备
- [ ] 设计应用图标 (1024x1024)
- [ ] 准备应用截图 (多种尺寸)
- [ ] 编写应用描述和关键词
- [ ] 准备上架材料和证书

## 开发时间估算 (Flutter版本)
- 第一阶段：2-3天 (Flutter环境搭建)
- 第二阶段：3-4天 (Firebase集成)
- 第三阶段：6-8天 (Flutter UI开发)
- 第四阶段：2-3天 (筛选功能)
- 第五阶段：3-5天 (打包上架)

**总计：16-23天**

## Flutter特有注意事项
1. 合理使用状态管理 (Provider/Riverpod)
2. 注意Widget重建性能优化
3. 使用const构造函数减少重建
4. 图片资源优化和缓存
5. 网络请求错误处理和重试机制
6. 适配不同屏幕尺寸和密度

## 核心依赖包
```yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
  firebase_storage: ^11.2.5
  provider: ^6.0.5
  image_picker: ^1.0.2
  cached_network_image: ^3.2.3
  dio: ^5.3.0
  shared_preferences: ^2.2.0
  permission_handler: ^10.4.3
```

## 后续扩展规划
- 微信/支付宝支付集成
- 推送通知功能
- 实时聊天功能 (Firebase Messaging)
- 地图集成 (高德/百度地图)
- iOS版本优化和上架
- 评价和评分系统
- 数据分析和统计
