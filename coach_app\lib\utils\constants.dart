import 'package:flutter/material.dart';

class AppColors {
  static const Color primary = Color(0xFF1976D2);
  static const Color primaryDark = Color(0xFF1565C0);
  static const Color accent = Color(0xFF03DAC6);
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFB00020);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF000000);
  static const Color onBackground = Color(0xFF000000);
  static const Color onError = Color(0xFFFFFFFF);
}

class AppStrings {
  static const String appName = '教练查找';
  static const String findCoach = '找教练';
  static const String publish = '发布';
  static const String profile = '我的';
  static const String search = '搜索教练...';
  static const String filter = '筛选';
  static const String specialty = '专长';
  static const String location = '地区';
  static const String experience = '经验';
  static const String priceRange = '价格区间';
  static const String contact = '联系方式';
  static const String description = '个人简介';
  static const String upload = '上传';
  static const String save = '保存';
  static const String cancel = '取消';
  static const String confirm = '确认';
  static const String loading = '加载中...';
  static const String noData = '暂无数据';
  static const String networkError = '网络错误';
  static const String uploadSuccess = '上传成功';
  static const String uploadFailed = '上传失败';
}

class AppSizes {
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  static const double avatarSmall = 40.0;
  static const double avatarMedium = 60.0;
  static const double avatarLarge = 80.0;
  static const double avatarXLarge = 120.0;
}

class AppTextStyles {
  static const TextStyle headline1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.onSurface,
  );
  
  static const TextStyle headline3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.onSurface,
  );
  
  static const TextStyle bodyText1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
  );
  
  static const TextStyle bodyText2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.onSurface,
  );
  
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: Colors.grey,
  );
}

class AppConstants {
  static const int pageSize = 20;
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> imageExtensions = ['jpg', 'jpeg', 'png', 'webp'];
  
  // 专长列表
  static const List<String> specialties = [
    '健身教练',
    '瑜伽教练',
    '游泳教练',
    '篮球教练',
    '足球教练',
    '网球教练',
    '羽毛球教练',
    '乒乓球教练',
    '跑步教练',
    '舞蹈教练',
    '武术教练',
    '其他',
  ];
  
  // 经验年限
  static const List<String> experienceRanges = [
    '1年以下',
    '1-3年',
    '3-5年',
    '5-10年',
    '10年以上',
  ];
  
  // 价格区间
  static const List<String> priceRanges = [
    '100元以下/小时',
    '100-200元/小时',
    '200-300元/小时',
    '300-500元/小时',
    '500元以上/小时',
  ];
}
