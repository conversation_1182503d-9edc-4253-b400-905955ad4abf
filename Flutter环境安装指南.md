# Flutter环境安装指南

## 第一步：下载Flutter SDK

1. 访问Flutter官网：https://flutter.dev/docs/get-started/install/windows
2. 下载Flutter SDK for Windows
3. 解压到一个目录，例如：`C:\flutter`

## 第二步：配置环境变量

1. 打开系统环境变量设置
2. 在系统变量中找到 `Path`
3. 添加Flutter的bin目录：`C:\flutter\bin`

## 第三步：验证安装

打开新的命令提示符或PowerShell，运行：
```bash
flutter doctor
```

## 第四步：安装Android Studio

1. 下载并安装Android Studio
2. 安装Android SDK
3. 配置Android模拟器

## 第五步：安装VS Code插件（可选）

1. 安装Flutter插件
2. 安装Dart插件

## 第六步：创建项目

```bash
flutter create coach_app
cd coach_app
flutter run
```

## 常见问题解决

### 1. Android许可证问题
```bash
flutter doctor --android-licenses
```

### 2. 网络问题
如果下载慢，可以配置镜像：
```bash
export PUB_HOSTED_URL=https://pub.flutter-io.cn
export FLUTTER_STORAGE_BASE_URL=https://storage.flutter-io.cn
```

### 3. 检查环境
```bash
flutter doctor -v
```

## 项目依赖配置

在 `pubspec.yaml` 中添加以下依赖：

```yaml
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
  firebase_storage: ^11.2.5
  provider: ^6.0.5
  image_picker: ^1.0.2
  cached_network_image: ^3.2.3
  dio: ^5.3.0
  shared_preferences: ^2.2.0
  permission_handler: ^10.4.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
```

## Firebase配置

1. 访问 https://console.firebase.google.com/
2. 创建新项目
3. 添加Android应用
4. 下载 `google-services.json` 文件
5. 将文件放置在 `android/app/` 目录下
6. 配置 `android/build.gradle` 和 `android/app/build.gradle`

## 下一步

安装完成后，可以开始开发教练应用：
1. 创建数据模型
2. 设置Firebase服务
3. 实现UI界面
4. 添加业务逻辑
